using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;
using Serilog;
using SmaTrendFollower.Services;
using SmaTrendFollower.Services.Refactored;
using SmaTrendFollower.Data;
using SmaTrendFollower.Examples;
using SmaTrendFollower.Console.Services;
using SmaTrendFollower.Infrastructure;
using SmaTrendFollower.Infrastructure.Security;
using SmaTrendFollower.Console.Configuration;
using SmaTrendFollower.Models;
using SmaTrendFollower.Configuration;
using Quartz;
using StackExchange.Redis;
using System.Threading.Channels;

namespace SmaTrendFollower.Configuration;

/// <summary>
/// Null object implementation of IOptimizedRedisConnectionService for when Redis is not available
/// </summary>
internal sealed class NullOptimizedRedisConnectionService : IOptimizedRedisConnectionService
{
    private readonly Microsoft.Extensions.Logging.ILogger _logger;

    public NullOptimizedRedisConnectionService(Microsoft.Extensions.Logging.ILogger logger)
    {
        _logger = logger;
    }

    public Task<IDatabase> GetDatabaseAsync(int database = 0)
    {
        _logger.LogWarning("Redis not available - GetDatabaseAsync returning null");
        return Task.FromResult<IDatabase>(null!);
    }

    public IDatabase GetDatabase(int db = 0)
    {
        _logger.LogWarning("Redis not available - GetDatabase returning null");
        return null!;
    }

    public Task<RedisHealthStatus> GetHealthStatusAsync()
    {
        return Task.FromResult(new RedisHealthStatus(false, "Redis not configured", null, null));
    }

    public RedisPerformanceMetrics GetPerformanceMetrics()
    {
        return new RedisPerformanceMetrics(0, 0, 0, 0, 0.0, DateTime.MinValue);
    }

    public Task WarmupConnectionPoolAsync()
    {
        _logger.LogWarning("Redis not available - WarmupConnectionPoolAsync skipped");
        return Task.CompletedTask;
    }

    public Task<RedisValue> GetAsync(string key, int database = 0)
    {
        _logger.LogWarning("Redis not available - GetAsync returning null for key: {Key}", key);
        return Task.FromResult(RedisValue.Null);
    }

    public Task SetAsync(string key, RedisValue value, TimeSpan? expiry = null, int database = 0)
    {
        _logger.LogWarning("Redis not available - SetAsync skipped for key: {Key}", key);
        return Task.CompletedTask;
    }

    public void Dispose() { }
}

/// <summary>
/// Centralized service configuration to eliminate duplicate registrations and ensure consistency
/// </summary>
public static class ServiceConfiguration
{
    /// <summary>
    /// Configure HTTP clients with Polly resilience policies
    /// - Retry policy: 3 attempts with exponential back-off (2s, 4s, 8s)
    /// - Circuit breaker: Opens after 5 failures for 30 seconds
    /// - Handles 5xx errors, network failures, and 429 rate limiting
    /// </summary>
    public static IServiceCollection AddPollyHttpClients(this IServiceCollection services, IConfiguration cfg)
    {
        var loggerFactory = LoggerFactory.Create(builder => builder.AddSerilog());
        var logger = loggerFactory.CreateLogger("PollyHttpClients");

        // Configure timeouts from configuration
        var timeouts = cfg.ConfigureTimeouts();
        timeouts.ValidateTimeouts(logger);

        // Unified rate limiting system for all external APIs
        services.AddSingleton<IUnifiedRateLimiter, UnifiedRateLimiter>();

        // API-specific rate limiters
        services.AddSingleton<IAlpacaRateLimiter, AlpacaRateLimiter>();
        services.AddSingleton<IPolygonRateLimiter, PolygonRateLimiter>();
        services.AddSingleton<IOpenAIRateLimiter, OpenAIRateLimiter>();
        services.AddSingleton<IGeminiRateLimiter, GeminiRateLimiter>();
        services.AddSingleton<IDiscordRateLimiter, DiscordRateLimiter>();
        services.AddSingleton<IFinnhubRateLimiter, FinnhubRateLimiter>();

        // Legacy Polygon rate limiter (for backward compatibility)
        services.AddSingleton<ISharedPolygonRateLimiter, SharedPolygonRateLimiter>();

        // Rate limit monitoring service
        services.AddSingleton<IRateLimitMonitoringService, RateLimitMonitoringService>();
        services.AddHostedService<RateLimitMonitoringService>(provider =>
            (RateLimitMonitoringService)provider.GetRequiredService<IRateLimitMonitoringService>());

        // Configure Polygon HTTP client with Polly policies
        services.AddHttpClient("Polygon")
            .ConfigureHttpClient(c =>
            {
                c.BaseAddress = new Uri("https://api.polygon.io/");
                c.Timeout = TimeSpan.FromSeconds(75); // Longer timeout for Polygon API to handle VIX data latency
                c.DefaultRequestHeaders.Add("User-Agent", "SmaTrendFollower/1.0");
                c.DefaultRequestHeaders.Add("Accept", "application/json");
            })
            .AddHttpMessageHandler(provider => new SmaTrendFollower.Monitoring.TimedHttpHandler("polygon"))
            .AddPolicyHandler(PollyPolicies.GetRetryPolicyWithLogging(logger, "Polygon"))
            .AddPolicyHandler(PollyPolicies.GetCircuitBreakerPolicyWithLogging(logger, "Polygon"));

        // Configure Alpaca HTTP client with Polly policies
        services.AddHttpClient("Alpaca")
            .ConfigureHttpClient(c =>
            {
                c.BaseAddress = new Uri("https://api.alpaca.markets");
                c.Timeout = timeouts.Http.StandardRequest;
                c.DefaultRequestHeaders.Add("User-Agent", "SmaTrendFollower/1.0");
                c.DefaultRequestHeaders.Add("Accept", "application/json");
            })
            .AddHttpMessageHandler(provider => new SmaTrendFollower.Monitoring.TimedHttpHandler("alpaca"))
            .AddPolicyHandler(PollyPolicies.GetRetryPolicyWithLogging(logger, "Alpaca"))
            .AddPolicyHandler(PollyPolicies.GetCircuitBreakerPolicyWithLogging(logger, "Alpaca"));

        logger.LogInformation("✅ Polly HTTP clients configured with resilience policies");
        return services;
    }

    /// <summary>
    /// Register core infrastructure services (factories, time providers)
    /// Note: HTTP clients are now configured by AddPollyHttpClients()
    /// </summary>
    public static IServiceCollection AddCoreInfrastructure(this IServiceCollection services)
    {
        // Check if running in dry run mode or hosted services are disabled
        var isDryRun = Environment.GetCommandLineArgs().Contains("--dry-run");
        var disableHostedServices = Environment.GetEnvironmentVariable("DISABLE_HOSTED_SERVICES") == "true";

        // HTTP client base configuration (AddPollyHttpClients should be called first)
        services.AddHttpClient();

        // Data staleness validation services
        services.AddOptions<DataStalenessConfiguration>()
            .Configure<IConfiguration>((settings, configuration) =>
            {
                configuration.GetSection("DataStaleness").Bind(settings);
            });
        // FIXED: Changed to Singleton to resolve DI lifetime mismatch with IEnhancedDataRetrievalService
        services.AddSingleton<IDataStalenessValidationService, DataStalenessValidationService>();

        // Configure connection pooling settings
        HttpClientConfigurationService.ConfigureConnectionPooling();

        // Rate limiting and client factories
        services.AddSingleton<IRateLimitPolicyFactory, RateLimitPolicyFactory>();
        services.AddSingleton<IAlpacaClientFactory, AlpacaClientFactory>();
        services.AddSingleton<IPolygonClientFactory, PolygonClientFactory>();
        services.AddSingleton<IPolygonWebSocketClient, PolygonWebSocketClient>();

        // Register real-time anomaly detection service (gracefully handles Redis unavailability)
        services.AddSingleton<AnomalyDetectorService>(provider =>
        {
            var connectionMultiplexer = provider.GetService<IConnectionMultiplexer>();
            var logger = provider.GetRequiredService<ILogger<AnomalyDetectorService>>();

            // Always create the service, but it will operate in degraded mode without Redis
            return new AnomalyDetectorService(connectionMultiplexer, logger);
        });

        // Bind options & register quote volatility guard
        services.AddSingleton<QuoteVolatilityGuard>(sp =>
        {
            var configuration = sp.GetRequiredService<IConfiguration>();
            var options = new QuoteVolatilityOptions();
            configuration.GetSection("QuoteVolatility").Bind(options);
            var connectionMultiplexer = sp.GetService<IConnectionMultiplexer>();
            return new QuoteVolatilityGuard(options, sp.GetRequiredService<ILogger<QuoteVolatilityGuard>>(), connectionMultiplexer);
        });

        // Register advanced Polygon WebSocket manager for batch subscriptions and rate limiting
        services.AddSingleton<PolygonWebSocketManager>(provider =>
        {
            var redisService = provider.GetService<IOptimizedRedisConnectionService>();
            var logger = provider.GetRequiredService<ILogger<PolygonWebSocketManager>>();
            var configuration = provider.GetRequiredService<IConfiguration>();
            var metricsService = provider.GetService<ITradingMetricsService>(); // Optional dependency
            var anomalyDetector = provider.GetService<AnomalyDetectorService>(); // Optional dependency
            var quoteVolatilityGuard = provider.GetService<QuoteVolatilityGuard>(); // Optional dependency
            var apiKey = GetPolygonApiKey(configuration);

            return new PolygonWebSocketManager(redisService!, logger, apiKey, metricsService, anomalyDetector, quoteVolatilityGuard);
        });
        
        // Time and session management
        services.AddSingleton<ITimeProvider, SystemTimeProvider>();
        services.AddSingleton<IMarketCalendarService, MarketCalendarService>();
        services.AddSingleton<IMarketSessionGuard, MarketSessionGuard>();

        // Trading environment detection
        services.AddSingleton<ITradingEnvironmentProvider, TradingEnvironmentProvider>();

        // Account streaming service for real-time account updates
        services.AddSingleton<IAccountSnapshotService, AccountStreamingService>();

        // Skip network-dependent hosted services for dry runs or when explicitly disabled to prevent startup hangs
        if (!isDryRun && !disableHostedServices)
        {
            services.AddHostedService(sp => (AccountStreamingService)sp.GetRequiredService<IAccountSnapshotService>());
        }

        // Trading cycle management - use scoped to avoid singleton dependency issues
        services.AddScoped<ITradingCycleManager>(provider =>
        {
            var marketDataService = provider.GetRequiredService<IMarketDataService>();
            var marketSessionGuard = provider.GetRequiredService<IMarketSessionGuard>();
            var logger = provider.GetRequiredService<ILogger<TradingCycleManager>>();
            var configuration = provider.GetRequiredService<Microsoft.Extensions.Configuration.IConfiguration>();
            var config = TradingCycleConfig.FromConfiguration(configuration);
            return new TradingCycleManager(marketDataService, marketSessionGuard, logger, config);
        });

        // API health monitoring
        services.AddSingleton<IApiHealthMonitor, ApiHealthMonitor>();

        // Bar recording for back-test replay
        services.AddSingleton<IBarRecorder, BarRecorder>();

        return services;
    }

    /// <summary>
    /// Register database contexts and caching services
    /// </summary>
    public static IServiceCollection AddDataServices(this IServiceCollection services, IConfiguration? cfg = null)
    {
        // Configuration options
        if (cfg != null)
        {
            services.Configure<UniverseCacheOptions>(cfg.GetSection("UniverseCache"));
        }

        // Database contexts - FIXED: Use DbContextFactory for thread-safe access
        services.AddDbContextFactory<IndexCacheDbContext>(options =>
        {
            var connectionString = "Data Source=index_cache.db";
            options.UseSqlite(connectionString);
        });
        services.AddDbContextFactory<StockBarCacheDbContext>(options =>
        {
            var connectionString = "Data Source=stock_cache.db";
            options.UseSqlite(connectionString);
        });

        // Cache services - FIXED: Use factory pattern for thread safety
        // FIXED: Changed to Singleton to resolve DI lifetime mismatch with MarketDataService
        services.AddSingleton<IIndexCacheService, SmaTrendFollower.Console.Services.ThreadSafeIndexCacheService>();
        services.AddSingleton<IStockBarCacheService, SmaTrendFollower.Console.Services.ThreadSafeStockBarCacheService>();
        services.AddScoped<ICacheMaintenanceService, CacheMaintenanceService>();

        // Bulk insert optimization service for ~5x performance improvement
        services.AddSingleton<IBulkInsertOptimizationService, BulkInsertOptimizationService>();
        services.AddScoped<IBulkInsertPerformanceTest, BulkInsertPerformanceTest>();

        // Momentum cache service - Register conditionally based on Redis availability
        services.AddSingleton<IMomentumCache>(provider =>
        {
            var configuration = provider.GetRequiredService<IConfiguration>();
            var loggerFactory = provider.GetRequiredService<ILoggerFactory>();
            var logger = loggerFactory.CreateLogger<RedisMomentumCache>();

            var redisConn =
                configuration.GetSection("Redis")["ConnectionString"]
                ?? Environment.GetEnvironmentVariable("REDIS_CONNECTION_STRING")
                ?? Environment.GetEnvironmentVariable("Redis__ConnectionString");

            if (!string.IsNullOrWhiteSpace(redisConn))
            {
                var redis = provider.GetService<IConnectionMultiplexer>();
                if (redis != null)
                {
                    return new RedisMomentumCache(redis, logger);
                }
            }

            logger.LogWarning("Redis not available, momentum cache will be disabled");
            return null!; // Return null when Redis is not configured
        });

        // Database initialization service
        services.AddScoped<IDatabaseInitializationService, DatabaseInitializationService>();

        // Database configuration service
        services.AddScoped<IDatabaseConfigurationService, DatabaseConfigurationService>();

        // Essential performance services (removed unused experimental optimization services)
        services.AddScoped<IOptimizedBulkOperationsService, OptimizedBulkOperationsService>(); // Used for database operations

        // Redis and state management - use null object pattern when Redis is unavailable
        services.AddSingleton<IOptimizedRedisConnectionService>(provider =>
        {
            var muxer = provider.GetService<IConnectionMultiplexer>();
            var configuration = provider.GetRequiredService<IConfiguration>();
            var logger = provider.GetRequiredService<ILogger<OptimizedRedisConnectionService>>();

            if (muxer == null)
            {
                logger.LogWarning("IConnectionMultiplexer not available, returning null OptimizedRedisConnectionService");
                return new NullOptimizedRedisConnectionService(logger);
            }

            return new OptimizedRedisConnectionService(muxer, configuration, logger);
        });

        // Register raw ConnectionMultiplexer for services that need it directly (optional)
        services.AddSingleton<StackExchange.Redis.IConnectionMultiplexer>(provider =>
        {
            var configuration = provider.GetRequiredService<IConfiguration>();
            var loggerFactory = provider.GetRequiredService<ILoggerFactory>();
            var logger = loggerFactory.CreateLogger("ServiceConfiguration");

            var redisConn =
                configuration.GetSection("Redis")["ConnectionString"]
                ?? Environment.GetEnvironmentVariable("REDIS_CONNECTION_STRING")
                ?? Environment.GetEnvironmentVariable("Redis__ConnectionString"); // env-hierarchy form

            if (string.IsNullOrWhiteSpace(redisConn))
            {
                logger.LogInformation("Redis connection string not found - running in degraded mode without Redis caching");
                logger.LogDebug("Redis not configured, using null connection multiplexer");
                return null!; // Return null when Redis is not configured
            }

            try
            {
                var configOptions = ConfigurationOptions.Parse(redisConn);
                configOptions.AbortOnConnectFail = false;
                configOptions.ConnectTimeout = 5000;
                configOptions.SyncTimeout = 2000;

                var connection = StackExchange.Redis.ConnectionMultiplexer.Connect(configOptions);
                logger.LogInformation("Redis connection established successfully");
                return connection;
            }
            catch (Exception ex)
            {
                logger.LogWarning(ex, "Failed to connect to Redis, continuing without Redis");
                return null!; // Return null on connection failure
            }
        });

        // Also register the concrete type for services that need it (optional)
        services.AddSingleton<StackExchange.Redis.ConnectionMultiplexer>(provider =>
        {
            var connectionMultiplexer = provider.GetService<StackExchange.Redis.IConnectionMultiplexer>();
            if (connectionMultiplexer == null)
            {
                return null!; // Return null when Redis is not configured
            }
            return connectionMultiplexer as StackExchange.Redis.ConnectionMultiplexer
                ?? throw new InvalidOperationException("ConnectionMultiplexer is not of expected concrete type");
        });

        services.AddSingleton<ILiveStateStore>(provider =>
        {
            var configuration = provider.GetRequiredService<IConfiguration>();
            var logger = provider.GetRequiredService<ILogger<LiveStateStore>>();

            // Check if Redis connection string is available
            var redisUrl = configuration.GetSection("Redis")["ConnectionString"]
                          ?? configuration["REDIS_URL"]
                          ?? Environment.GetEnvironmentVariable("REDIS_CONNECTION_STRING")
                          ?? Environment.GetEnvironmentVariable("Redis__ConnectionString");

            if (string.IsNullOrWhiteSpace(redisUrl))
            {
                logger.LogWarning("Redis connection string not found, LiveStateStore will not be functional");
                return null!; // Return null when Redis is not configured
            }

            return new LiveStateStore(configuration, logger);
        });
        // FIXED: Changed to Singleton to resolve DI lifetime mismatch with StateFlushService
        services.AddSingleton<IBarStore, HistoricalBarStore>();
        services.AddScoped<IRedisWarmingService, RedisWarmingService>();

        return services;
    }

    /// <summary>
    /// Register market data and universe services
    /// </summary>
    public static IServiceCollection AddMarketDataServices(this IServiceCollection services)
    {
        // Market data services
        services.AddSingleton<IVixFallbackService, VixFallbackService>();
        services.AddSingleton<ISyntheticVixService>(provider =>
        {
            var marketDataService = provider.GetRequiredService<IMarketDataService>();
            var connectionMultiplexer = provider.GetService<ConnectionMultiplexer>();
            var logger = provider.GetRequiredService<ILogger<SyntheticVixService>>();

            if (connectionMultiplexer == null)
            {
                logger.LogWarning("Redis not available, SyntheticVixService will not be functional");
                return null!; // Return null when Redis is not configured
            }

            return new SyntheticVixService(marketDataService, connectionMultiplexer, logger);
        });
        services.AddSingleton<SyntheticVixTrainer>(provider =>
        {
            var connectionMultiplexer = provider.GetService<ConnectionMultiplexer>();
            var logger = provider.GetRequiredService<ILogger<SyntheticVixTrainer>>();

            if (connectionMultiplexer == null)
            {
                logger.LogWarning("Redis not available, SyntheticVixTrainer will not be functional");
                return null!; // Return null when Redis is not configured
            }

            var marketDataService = provider.GetRequiredService<IMarketDataService>();
            return new SyntheticVixTrainer(marketDataService, connectionMultiplexer, logger);
        }); // Quartz will create job instances
        services.AddSingleton<IMarketDataService, MarketDataService>();
        services.AddSingleton<IStreamingDataService, StreamingDataService>();

        // Universe providers
        services.AddSingleton<IUniverseProvider, HybridUniverseProvider>();
        // FIXED: Changed to Singleton to resolve DI lifetime mismatch with HybridUniverseProvider
        services.AddSingleton<IDynamicUniverseProvider, DynamicUniverseProvider>();
        services.AddHttpClient<IUniverseFetcherService, UniverseFetcherService>(client =>
        {
            client.BaseAddress = new Uri("https://api.polygon.io/");
            client.Timeout = TimeSpan.FromSeconds(30);
        });

        // Polygon universe services for dynamic universe management
        services.AddSingleton<IPolygonSymbolUniverseService, PolygonSymbolUniverseService>();
        services.AddSingleton<IPolygonSymbolSnapshotService, PolygonSymbolSnapshotService>();
        services.AddSingleton<IDailyUniverseRefreshService, DailyUniverseRefreshService>();
        services.AddSingleton<IWebSocketSymbolSubscriptionManager, WebSocketSymbolSubscriptionManager>();
        services.AddSingleton<IMarketScheduleCoordinatorService, MarketScheduleCoordinatorService>();

        // Universe jobs for scheduling and startup bootstrap
        services.AddSingleton<SmaTrendFollower.Scheduling.IDynamicUniverseFilterJob, SmaTrendFollower.Scheduling.DynamicUniverseFilterJob>();

        return services;
    }

    /// <summary>
    /// Register safety and risk management services
    /// </summary>
    public static IServiceCollection AddSafetyServices(this IServiceCollection services)
    {
        services.AddSingleton<ISafetyConfigurationService, SafetyConfigurationService>();
        services.AddScoped<IDynamicSafetyConfigurationService, DynamicSafetyConfigurationService>();
        services.AddSingleton<ITradingSafetyGuard, TradingSafetyGuard>();

        return services;
    }

    /// <summary>
    /// Register core trading strategy services
    /// </summary>
    public static IServiceCollection AddTradingServices(this IServiceCollection services)
    {
        // Signal generation and filtering
        // FIXED: Changed to Singleton to resolve DI lifetime mismatch with ISignalGenerator
        services.AddSingleton<IMomentumFilter, MomentumFilter>();
        // FIXED: Changed to Singleton to resolve DI lifetime mismatch with ISignalGenerator
        services.AddSingleton<IVolatilityFilter, VolatilityFilter>();
        // FIXED: Changed to Singleton to resolve DI lifetime mismatch with ISignalGenerator
        services.AddSingleton<IPositionSizer, DynamicPositionSizer>();
        // FIXED: Changed to Singleton to resolve DI lifetime mismatch with LiveSignalIntelligence and BacktestReplayEngine
        services.AddSingleton<ISignalGenerator, EnhancedSignalGenerator>();

        // Add adaptive signal configuration and services
        services.AddSingleton<SmaTrendFollower.Configuration.AdaptiveSignalConfiguration>(provider =>
            new SmaTrendFollower.Configuration.AdaptiveSignalConfiguration());
        services.AddSingleton<SmaTrendFollower.Configuration.RobustSignalConfiguration>(provider =>
            new SmaTrendFollower.Configuration.RobustSignalConfiguration());
        services.AddScoped<IAdaptiveSignalGenerator, AdaptiveSignalGenerator>();
        services.AddScoped<IRobustSignalGenerationService, RobustSignalGenerationService>();

        // Risk and portfolio management
        services.AddScoped<IRiskManager>(provider =>
        {
            var clientFactory = provider.GetRequiredService<IAlpacaClientFactory>();
            var accountSnapshot = provider.GetRequiredService<IAccountSnapshotService>();
            var logger = provider.GetRequiredService<ILogger<RiskManager>>();
            var positionSizer = provider.GetService<PositionSizerService>();

            return new RiskManager(clientFactory, accountSnapshot, logger, positionSizer);
        });
        services.AddScoped<IPortfolioGate, PortfolioGate>();
        // FIXED: Changed to Singleton to resolve DI lifetime mismatch with TradeExecutor
        services.AddSingleton<IStopManager, StopManager>();

        // Market regime detection
        // FIXED: Changed to Singleton to resolve DI lifetime mismatch with IBreadthService
        services.AddSingleton<IMarketRegimeService, MarketRegimeService>();

        // Trade execution with safety wrapper (consolidated from AddTradeExecutionServices)
        services.AddSingleton<TradeExecutor>(); // Original executor
        services.AddSingleton<ITradeExecutor>(provider =>
        {
            var innerExecutor = provider.GetRequiredService<TradeExecutor>();
            var safetyGuard = provider.GetRequiredService<ITradingSafetyGuard>();
            var logger = provider.GetRequiredService<ILogger<SafeTradeExecutor>>();
            return new SafeTradeExecutor(innerExecutor, safetyGuard, logger);
        });

        return services;
    }

    /// <summary>
    /// Register machine learning services for signal enhancement
    /// </summary>
    public static IServiceCollection AddMachineLearningServices(this IServiceCollection services, IConfiguration? cfg = null)
    {
        // Check if running in dry run mode or hosted services are disabled
        var isDryRun = Environment.GetCommandLineArgs().Contains("--dry-run");
        var disableHostedServices = Environment.GetEnvironmentVariable("DISABLE_HOSTED_SERVICES") == "true";

        // Configuration options
        if (cfg != null)
        {
            services.Configure<SlippageOptions>(cfg.GetSection("Slippage"));
        }

        // ML Context (singleton for performance)
        services.AddSingleton<Microsoft.ML.MLContext>(provider => new Microsoft.ML.MLContext(seed: 42));

        // ML Features Database Context
        services.AddDbContextFactory<SmaTrendFollower.Data.MLFeaturesDbContext>(options =>
        {
            var connectionString = "Data Source=ml_features.db";
            options.UseSqlite(connectionString);
        });

        // ML Services
        services.AddScoped<SmaTrendFollower.MachineLearning.DataPrep.IFeatureExportService,
                          SmaTrendFollower.MachineLearning.DataPrep.FeatureExportService>();
        services.AddSingleton<SmaTrendFollower.MachineLearning.Prediction.ISignalRanker>(provider =>
        {
            var mlContext = new Microsoft.ML.MLContext(seed: 42);
            var configuration = provider.GetRequiredService<IConfiguration>();
            var logger = provider.GetRequiredService<ILogger<SmaTrendFollower.MachineLearning.Prediction.SignalRanker>>();
            var redisService = provider.GetService<OptimizedRedisConnectionService>();

            return new SmaTrendFollower.MachineLearning.Prediction.SignalRanker(
                mlContext, configuration, logger, redisService);
        });

        // Position Sizing Service
        services.AddSingleton<PositionSizerService>(provider =>
        {
            var logger = provider.GetRequiredService<ILogger<PositionSizerService>>();
            var configuration = provider.GetRequiredService<IConfiguration>();
            var redisService = provider.GetService<OptimizedRedisConnectionService>();

            return new PositionSizerService(logger, configuration, redisService);
        });

        // ML-Enhanced Signal Generator (alternative to standard EnhancedSignalGenerator)
        services.AddScoped<MLEnhancedSignalGenerator>();

        // Slippage Forecaster Service
        services.AddSingleton<SmaTrendFollower.MachineLearning.Prediction.ISlippageForecasterService>(provider =>
        {
            var logger = provider.GetRequiredService<ILogger<SmaTrendFollower.MachineLearning.Prediction.SlippageForecasterService>>();
            var configuration = provider.GetRequiredService<IConfiguration>();
            var redisService = provider.GetService<OptimizedRedisConnectionService>();

            return new SmaTrendFollower.MachineLearning.Prediction.SlippageForecasterService(
                logger, redisService, configuration);
        });

        // Regime Classifier Service
        services.AddSingleton<IRegimeClassifierService, RegimeClassifierService>();

        // FinBERT Sentiment Pipeline (unified channel for Alpaca + Brave news)
        if (cfg != null)
        {
            services.Configure<FinbertOptions>(cfg.GetSection("Finbert"));
        }
        var channel = Channel.CreateUnbounded<HeadlineItem>();
        services.AddSingleton(channel);
        services.AddSingleton<NewsSentimentService>();
        services.AddHostedService<FinbertWorker>();

        // FinBERT HttpClient
        services.AddHttpClient("Finbert")
            .ConfigureHttpClient((sp, c) =>
            {
                var o = sp.GetRequiredService<IOptions<FinbertOptions>>().Value;
                c.BaseAddress = new Uri(o.BaseUrl);
                c.Timeout = TimeSpan.FromSeconds(2);
            })
            .AddHttpMessageHandler(_ => new SmaTrendFollower.Monitoring.TimedHttpHandler("finbert"));

        // LLM sentiment overlay
        if (cfg != null)
        {
            services.Configure<LlmSentimentOptions>(cfg.GetSection("LlmSentiment"));
        }
        services.AddHttpClient("OpenAI")
            .ConfigureHttpClient((sp, c) =>
            {
                c.BaseAddress = new Uri("https://api.openai.com");
                c.DefaultRequestHeaders.Add("Authorization", $"Bearer {Environment.GetEnvironmentVariable("OPENAI_API_KEY")}");
                c.Timeout = TimeSpan.FromSeconds(sp.GetRequiredService<IOptions<LlmSentimentOptions>>().Value.TimeoutSeconds);
            })
            .AddHttpMessageHandler(_ => new SmaTrendFollower.Monitoring.TimedHttpHandler("openai"));

        services.AddHttpClient("Gemini")
            .ConfigureHttpClient((sp, c) =>
            {
                var key = Environment.GetEnvironmentVariable("GEMINI_API_KEY");
                c.BaseAddress = new Uri("https://generativelanguage.googleapis.com");
                c.Timeout = TimeSpan.FromSeconds(sp.GetRequiredService<IOptions<LlmSentimentOptions>>().Value.TimeoutSeconds);
                c.DefaultRequestHeaders.Add("x-goog-api-key", key);
            })
            .AddHttpMessageHandler(_ => new SmaTrendFollower.Monitoring.TimedHttpHandler("gemini"));

        services.AddSingleton<LlmSentimentService>();

        // Brave News Poller (adds second headline stream to FinBERT pipeline)
        if (cfg != null)
        {
            services.Configure<BraveOptions>(cfg.GetSection("Brave"));
        }
        services.AddHttpClient("BraveNews")
            .ConfigureHttpClient((sp, c) =>
            {
                var o = sp.GetRequiredService<IOptions<BraveOptions>>().Value;
                c.BaseAddress = new Uri(o.BaseUrl);
                c.Timeout = TimeSpan.FromSeconds(4);
                if (!string.IsNullOrWhiteSpace(o.ApiKey))
                    c.DefaultRequestHeaders.Add("X-API-Key", o.ApiKey);
            })
            .AddHttpMessageHandler(_ => new SmaTrendFollower.Monitoring.TimedHttpHandler("bravenews"));

        services.AddHostedService<BraveNewsPoller>();   // kicks off on host start

        // Legacy Gemini News Sentiment Analysis Services (kept for compatibility)
        services.AddHttpClient<IGeminiClient, GeminiClient>()
            .AddHttpMessageHandler(provider => new SmaTrendFollower.Monitoring.TimedHttpHandler("gemini"));
        services.AddScoped<IMomentumModelTrainer, MomentumModelTrainer>();

        // Brave Search Services
        services.AddHttpClient("brave-search")
            .ConfigureHttpClient(c =>
            {
                c.Timeout = TimeSpan.FromSeconds(30);
                c.DefaultRequestHeaders.Add("User-Agent", "SmaTrendFollower/1.0");
            })
            .AddHttpMessageHandler(provider => new SmaTrendFollower.Monitoring.TimedHttpHandler("brave-search"));
        services.AddSingleton<IBraveSearchService, BraveSearchService>();

        // Simple Slippage Forecaster (linear regression model)
        services.AddSingleton<ISlippageForecaster, SlippageForecaster>();

        // Model trainers & bootstrap
        services.AddSingleton<SlippageModelTrainer>(); // Updated to remove interface

        // Skip network-dependent hosted services for dry runs or when explicitly disabled to prevent startup hangs
        if (!isDryRun && !disableHostedServices)
        {
            // Start FinBERT news polling when host starts
            services.AddHostedService(sp =>
            {
                var poller = sp.GetRequiredService<NewsSentimentService>();
                var logger = sp.GetRequiredService<ILogger<AsyncPollingService>>();
                return new AsyncPollingService(ct => poller.PollAsync(ct), logger);
            });

            services.AddHostedService<ModelAssetBootstrapService>();
        }

        return services;
    }

    /// <summary>
    /// Register production-ready enhanced trading services (volatility, options, notifications, real-time streaming)
    /// </summary>
    public static IServiceCollection AddEnhancedTradingServices(this IServiceCollection services, IConfiguration? configuration = null)
    {
        // Configuration for slippage forecaster
        if (configuration != null)
        {
            services.Configure<SlippageOptions>(configuration.GetSection("Slippage"));
        }

        services.AddScoped<IVolatilityManager, VolatilityManager>();
        services.AddScoped<IOptionsStrategyManager, OptionsStrategyManager>();
        // FIXED: Changed to Singleton to resolve DI lifetime mismatch with WheelStrategyEngine
        services.AddSingleton<IDiscordNotificationService, DiscordNotificationService>();

        // Essential real-time services (removed unused experimental services)
        services.AddSingleton<IBreadthService, BreadthService>(); // Used by regime detection
        services.AddScoped<IExecutionQAService, ExecutionQAService>(); // Used by trading validation

        // Add missing tick stream service for real-time data processing
        services.AddSingleton<ITickStreamService, TickStreamService>();

        // Phase 6: Advanced Filters and Reactive Triggers (Polygon Developer Roadmap)
        services.AddScoped<IVWAPMonitorService, VWAPMonitorService>();
        services.AddScoped<ITickVolatilityGuard, TickVolatilityGuard>();
        services.AddScoped<IRealTimeBreakoutSignal, RealTimeBreakoutSignal>();
        services.AddScoped<IMicrostructurePatternDetector, MicrostructurePatternDetector>();

        // Phase 6: Real-Time Intelligence & Signal Architecture (New Roadmap Services)
        services.AddScoped<IIndexRegimeService, IndexRegimeService>();
        // FIXED: Changed to Singleton to resolve DI lifetime mismatch with WheelStrategyEngine
        services.AddSingleton<IVIXResolverService, VIXResolverService>();
        services.AddScoped<IBreadthMonitorService, BreadthMonitorService>();
        services.AddScoped<IRealTimeExecutionService, RealTimeExecutionService>();

        // Essential trading tools (removed unused experimental services)
        services.AddSingleton<ISlippageEstimator, SlippageEstimator>(); // Used by TradeExecutor
        services.AddSingleton<ISlippageForecaster, SlippageForecaster>(); // Used by TradeExecutor

        return services;
    }

    // Removed: AddTradeExecutionServices (consolidated into AddTradingServices)

    /// <summary>
    /// Register essential monitoring services for production trading
    /// </summary>
    public static IServiceCollection AddMonitoringServices(this IServiceCollection services, IConfiguration configuration)
    {
        // Check if running in dry run mode to skip network-dependent services
        var isDryRun = Environment.GetCommandLineArgs().Contains("--dry-run");
        var disableHostedServices = Environment.GetEnvironmentVariable("DISABLE_HOSTED_SERVICES") == "true";

        // VWAP options configuration
        services.Configure<VwapOptions>(configuration.GetSection("VWAP"));

        // Essential API rate limit monitoring (used by trading services)
        services.AddSingleton<IApiRateLimitMonitor, ApiRateLimitMonitor>();
        services.AddSingleton<IApiRateLimitWrapper, ApiRateLimitWrapper>();
        if (!disableHostedServices)
        {
            services.AddHostedService<ApiRateLimitMonitor>(provider =>
                (ApiRateLimitMonitor)provider.GetRequiredService<IApiRateLimitMonitor>());
        }

        // Essential trailing stop management (used by trading services)
        services.AddSingleton<RealTimeTrailingStopManager>();
        services.AddSingleton<ITrailingStopManager>(provider => provider.GetRequiredService<RealTimeTrailingStopManager>());

        // State management - only register hosted services if not disabled
        if (!disableHostedServices)
        {
            services.AddHostedService<RealTimeTrailingStopManager>(provider => provider.GetRequiredService<RealTimeTrailingStopManager>());
            services.AddHostedService<StateFlushService>();
        }

        // Startup data bootstrap - ensures universe data is available on cold starts (only if Redis is available)
        services.AddSingleton<SmaTrendFollower.Infrastructure.StartupDataBootstrapper>(provider =>
        {
            var connectionMultiplexer = provider.GetService<ConnectionMultiplexer>();
            var fetcher = provider.GetRequiredService<IUniverseFetcherService>();
            var filter = provider.GetRequiredService<SmaTrendFollower.Scheduling.IDynamicUniverseFilterJob>();
            var logger = provider.GetRequiredService<ILogger<SmaTrendFollower.Infrastructure.StartupDataBootstrapper>>();

            if (connectionMultiplexer == null)
            {
                logger.LogWarning("Redis not available, StartupDataBootstrapper will not be functional");
                return null!; // Return null when Redis is not configured
            }

            return new SmaTrendFollower.Infrastructure.StartupDataBootstrapper(connectionMultiplexer, fetcher, filter, logger);
        });

        // Register as hosted service only if the instance is not null and hosted services are not disabled
        if (!disableHostedServices)
        {
            services.AddHostedService<SmaTrendFollower.Infrastructure.StartupDataBootstrapper>(provider =>
            {
                return provider.GetService<SmaTrendFollower.Infrastructure.StartupDataBootstrapper>()!;
            });
        }

        // Skip network-dependent hosted services for dry runs or when explicitly disabled to prevent startup hangs
        if (!isDryRun && !disableHostedServices)
        {
            // Universe warming service - performs initial refresh and schedules daily refreshes
            services.AddHostedService<UniverseWarmService>();

            // Polygon universe background services
            services.AddHostedService<DailyUniverseRefreshService>(provider => (provider.GetRequiredService<IDailyUniverseRefreshService>() as DailyUniverseRefreshService)!);
            services.AddHostedService<WebSocketSymbolSubscriptionManager>(provider => (provider.GetRequiredService<IWebSocketSymbolSubscriptionManager>() as WebSocketSymbolSubscriptionManager)!);
            services.AddHostedService<MarketScheduleCoordinatorService>(provider => (provider.GetRequiredService<IMarketScheduleCoordinatorService>() as MarketScheduleCoordinatorService)!);
        }

        return services;
    }

    /// <summary>
    /// Register essential optimization services (removed unused experimental services)
    /// </summary>
    public static IServiceCollection AddAdaptiveOptimizationServices(this IServiceCollection services)
    {
        // Only keep essential optimization services that are actually used
        // Removed: IPerformanceAnalysisService, IAdaptiveLearningService, IStrategyOptimizationOrchestrator, IBacktestingEngine
        // These were experimental and never injected into core trading services

        return services;
    }

    /// <summary>
    /// Register Quartz.NET scheduling services for automated universe management and VIX training
    /// </summary>
    public static IServiceCollection AddSchedulingServices(this IServiceCollection services)
    {
        // Add Quartz services
        services.AddQuartz(q =>
        {
            // UseMicrosoftDependencyInjectionJobFactory() is now the default and obsolete

            // Configure the UniverseJobs job
            var jobKey = new JobKey("UniverseJob");
            q.AddJob<SmaTrendFollower.Scheduling.UniverseJobs>(opts => opts.WithIdentity(jobKey));

            // Schedule daily at 8:30 AM ET (12:30 PM UTC)
            q.AddTrigger(t => t
                .ForJob(jobKey)
                .WithIdentity("UniverseTrigger")
                .StartNow()
                .WithCronSchedule("0 30 12 * * ?") // Daily at 12:30 UTC (8:30 AM ET)
                .WithDescription("Daily universe refresh at 8:30 AM ET"));

            // Configure the SyntheticVixTrainer job
            var vixTrainerJobKey = new JobKey("VixTrainer");
            q.AddJob<SyntheticVixTrainer>(opts => opts.WithIdentity(vixTrainerJobKey));

            // Schedule weekly on Sunday at 6:00 PM ET (22:00 UTC)
            q.AddTrigger(t => t
                .ForJob(vixTrainerJobKey)
                .WithIdentity("VixTrainerTrigger")
                .StartNow()
                .WithCronSchedule("0 0 22 ? * SUN") // Weekly on Sunday at 22:00 UTC (6:00 PM ET)
                .WithDescription("Weekly synthetic VIX regression training on Sunday at 6:00 PM ET"));

            // Configure the RedisCleanupService job
            var redisCleanupJobKey = new JobKey("RedisCleanup");
            q.AddJob<RedisCleanupService>(opts => opts.WithIdentity(redisCleanupJobKey));

            // Schedule daily at 2:00 AM ET (6:00 AM UTC)
            q.AddTrigger(t => t
                .ForJob(redisCleanupJobKey)
                .WithIdentity("RedisCleanupTrigger")
                .StartNow()
                .WithCronSchedule("0 0 6 * * ?") // Daily at 6:00 AM UTC (2:00 AM ET)
                .WithDescription("Daily Redis key hygiene cleanup at 2:00 AM ET"));

            // Configure the ClearHaltGaugeJob
            var haltGaugeJobKey = new JobKey("ClearHaltGauge");
            q.AddJob<SmaTrendFollower.Scheduling.ClearHaltGaugeJob>(opts => opts.WithIdentity(haltGaugeJobKey));

            // Schedule daily at 2:05 AM ET (6:05 AM UTC) - after Redis cleanup
            q.AddTrigger(t => t
                .ForJob(haltGaugeJobKey)
                .WithIdentity("ClearHaltGaugeTrigger")
                .StartNow()
                .WithCronSchedule("0 5 6 * * ?") // Daily at 6:05 AM UTC (2:05 AM ET)
                .WithDescription("Daily halt gauge cleanup at 2:05 AM ET"));

            // Configure the ML Model Retrainer job
            var mlRetrainerJobKey = new JobKey("MLModelRetrainer");
            q.AddJob<SmaTrendFollower.Scheduling.MLModelRetrainerJob>(opts => opts.WithIdentity(mlRetrainerJobKey));

            // Schedule weekly on Sunday at 6:00 PM ET (10:00 PM UTC)
            q.AddTrigger(t => t
                .ForJob(mlRetrainerJobKey)
                .WithIdentity("MLRetrainerTrigger")
                .StartNow()
                .WithCronSchedule("0 0 22 ? * SUN") // Weekly on Sunday at 10:00 PM UTC (6:00 PM ET)
                .WithDescription("Weekly ML model retraining on Sunday at 6:00 PM ET"));

            // Configure the Position Sizer Retrainer job
            var positionSizerJobKey = new JobKey("PositionSizerRetrainer");
            q.AddJob<SmaTrendFollower.Scheduling.PositionSizerRetrainerJob>(opts => opts.WithIdentity(positionSizerJobKey));

            // Schedule weekly on Sunday at 6:30 PM ET (10:30 PM UTC) - 30 minutes after signal ranker
            q.AddTrigger(t => t
                .ForJob(positionSizerJobKey)
                .WithIdentity("PositionSizerRetrainerTrigger")
                .StartNow()
                .WithCronSchedule("0 30 22 ? * SUN") // Weekly on Sunday at 10:30 PM UTC (6:30 PM ET)
                .WithDescription("Weekly position sizer retraining on Sunday at 6:30 PM ET"));

            // Configure the Slippage Forecaster Retrainer job
            var slippageForecasterJobKey = new JobKey("SlippageForecasterRetrainer");
            q.AddJob<SmaTrendFollower.Scheduling.SlippageForecasterRetrainerJob>(opts => opts.WithIdentity(slippageForecasterJobKey));

            // Schedule weekly on Sunday at 6:55 PM ET (10:55 PM UTC) - 25 minutes after position sizer
            q.AddTrigger(t => t
                .ForJob(slippageForecasterJobKey)
                .WithIdentity("SlippageForecasterRetrainerTrigger")
                .StartNow()
                .WithCronSchedule("0 55 22 ? * SUN") // Weekly on Sunday at 10:55 PM UTC (6:55 PM ET)
                .WithDescription("Weekly slippage forecaster retraining on Sunday at 6:55 PM ET"));

            // Configure the Regime Classifier Runner job (daily detection)
            var regimeRunnerJobKey = new JobKey("RegimeClassifierRunner");
            q.AddJob<SmaTrendFollower.Scheduling.RegimeClassifierRunner>(opts => opts.WithIdentity(regimeRunnerJobKey));

            // Schedule daily at 8:35 AM ET (12:35 PM UTC) - after universe refresh
            q.AddTrigger(t => t
                .ForJob(regimeRunnerJobKey)
                .WithIdentity("RegimeClassifierRunnerTrigger")
                .StartNow()
                .WithCronSchedule("0 35 12 * * ?") // Daily at 12:35 PM UTC (8:35 AM ET)
                .WithDescription("Daily regime classification at 8:35 AM ET"));

            // Configure the Regime Classifier Retrainer job (weekly training)
            var regimeRetrainerJobKey = new JobKey("RegimeClassifierRetrainer");
            q.AddJob<SmaTrendFollower.Scheduling.RegimeClassifierRetrainer>(opts => opts.WithIdentity(regimeRetrainerJobKey));

            // Schedule weekly on Sunday at 6:45 PM ET (10:45 PM UTC) - between position sizer and slippage forecaster
            q.AddTrigger(t => t
                .ForJob(regimeRetrainerJobKey)
                .WithIdentity("RegimeClassifierRetrainerTrigger")
                .StartNow()
                .WithCronSchedule("0 45 22 ? * SUN") // Weekly on Sunday at 10:45 PM UTC (6:45 PM ET)
                .WithDescription("Weekly regime classifier retraining on Sunday at 6:45 PM ET"));

            // Configure the DrawdownMonitor job (every 5 minutes during trading hours)
            var drawdownMonitorJobKey = new JobKey("DrawdownMonitor");
            q.AddJob<SmaTrendFollower.Services.DrawdownMonitor>(opts => opts.WithIdentity(drawdownMonitorJobKey));

            // Schedule every 5 minutes
            q.AddTrigger(t => t
                .ForJob(drawdownMonitorJobKey)
                .WithIdentity("DrawdownMonitorTrigger")
                .StartNow()
                .WithSimpleSchedule(s => s
                    .WithIntervalInMinutes(5)
                    .RepeatForever())
                .WithDescription("Monitor P&L drawdown every 5 minutes"));
        });

        // Add Quartz hosted service only if hosted services are not disabled
        var disableHostedServices = Environment.GetEnvironmentVariable("DISABLE_HOSTED_SERVICES") == "true";
        if (!disableHostedServices)
        {
            services.AddQuartzHostedService(opt =>
            {
                opt.WaitForJobsToComplete = true;
                opt.AwaitApplicationStarted = true;
            });
        }

        // Register job classes as services for manual execution
        services.AddTransient<SmaTrendFollower.Scheduling.MLModelRetrainerJob>();
        services.AddTransient<SmaTrendFollower.Scheduling.PositionSizerRetrainerJob>();
        services.AddTransient<SmaTrendFollower.Scheduling.SlippageForecasterRetrainerJob>();
        services.AddTransient<SmaTrendFollower.Scheduling.UniverseJobs>();
        services.AddTransient<SmaTrendFollower.Scheduling.ClearHaltGaugeJob>();
        services.AddTransient<SmaTrendFollower.Scheduling.RegimeClassifierRunner>();
        services.AddTransient<SmaTrendFollower.Scheduling.RegimeClassifierRetrainer>();
        services.AddTransient<SmaTrendFollower.Scheduling.ManualRegimeClassifierTrigger>();

        // Register ML trainer services
        services.AddScoped<SmaTrendFollower.MachineLearning.ModelTraining.SlippageForecasterTrainer>();

        return services;
    }



    /// <summary>
    /// Register backtesting services (removed unused experimental services)
    /// </summary>
    public static IServiceCollection AddBacktestingServices(this IServiceCollection services)
    {
        // Removed unused backtesting services that were never used in core trading:
        // - PolygonTickLoader, IVirtualTimeProvider, BacktestReplayEngine
        // These were experimental and not integrated into the main trading flow
        return services;
    }

    /// <summary>
    /// Register backtesting services with web API support (for web applications only)
    /// </summary>
    public static IServiceCollection AddBacktestingServicesWithWebApi(this IServiceCollection services)
    {
        services.AddBacktestingServices();
        services.AddControllers(); // For web API dashboard
        return services;
    }

    /// <summary>
    /// Register all services for production-ready trading system (simplified architecture)
    /// </summary>
    public static IServiceCollection AddFullTradingSystem(this IServiceCollection services, IConfiguration configuration)
    {
        return services
            .AddPollyHttpClients(configuration) // Polly resilience policies
            .AddCoreInfrastructure()
            .AddDataServices(configuration)
            .AddMarketDataServices()
            .AddSafetyServices()
            .AddTradingServices() // Includes trade execution (consolidated)
            .AddMachineLearningServices(configuration) // ML-enhanced signal generation
            .AddEnhancedTradingServices(configuration)
            // TEMPORARILY DISABLED: .AddEnhancedTradingSystemServices(configuration) // Causes circular dependency with decorator pattern
            .AddEnhancedServicesMigration() // NEW: Migration management for enhanced services
            .AddMonitoringServices(configuration) // Essential monitoring only
            .AddAdaptiveOptimizationServices() // Simplified (experimental services removed)
            .AddSchedulingServices() // Automated universe management
            .AddBacktestingServices() // Simplified (experimental services removed)
            .AddWheelStrategyServices(configuration) // Wheel options strategy
            .AddObservabilityServices(configuration); // Prometheus metrics and health checks
    }

    /// <summary>
    /// Register wheel options strategy services
    /// </summary>
    public static IServiceCollection AddWheelStrategyServices(this IServiceCollection services, IConfiguration configuration)
    {
        // Check if running in dry run mode or hosted services are disabled
        var isDryRun = Environment.GetCommandLineArgs().Contains("--dry-run");
        var disableHostedServices = Environment.GetEnvironmentVariable("DISABLE_HOSTED_SERVICES") == "true";

        // Configure wheel strategy
        services.Configure<WheelStrategyConfig>(configuration.GetSection("WheelStrategy"));
        services.Configure<OptionsWheelOptions>(configuration.GetSection("OptionsWheel"));

        // Register rate limiting helper for Finnhub (30 req/sec AND 60 req/min limits)
        services.AddSingleton<FinnhubRateLimitHelper>();

        // Register earnings calendar services with HttpClient
        services.AddHttpClient<FinnhubEarningsCalendar>()
                .SetHandlerLifetime(TimeSpan.FromMinutes(30));

        services.AddHttpClient<PolygonEarningsCalendar>()
                .SetHandlerLifetime(TimeSpan.FromMinutes(30));

        services.AddHttpClient<FallbackEarningsCalendar>()
                .SetHandlerLifetime(TimeSpan.FromMinutes(30));

        // Register memory cache for earnings data caching
        services.AddMemoryCache();

        // Register the composite earnings calendar as the main interface
        // FIXED: Changed to Singleton to resolve DI lifetime mismatch with WheelStrategyEngine
        services.AddSingleton<IEarningsCalendar, CompositeEarningsCalendar>();

        services.AddScoped<IWheelStrategyEngine, WheelStrategyEngine>();

        // Skip network-dependent hosted services for dry runs or when explicitly disabled to prevent startup hangs
        if (!isDryRun && !disableHostedServices)
        {
            // Register WheelStrategyEngine as hosted service for automated execution
            services.AddHostedService<WheelStrategyEngine>(provider => (WheelStrategyEngine)provider.GetRequiredService<IWheelStrategyEngine>());
        }

        return services;
    }

    /// <summary>
    /// Register observability services including Prometheus metrics and health checks
    /// </summary>
    public static IServiceCollection AddObservabilityServices(this IServiceCollection services, IConfiguration configuration)
    {
        // Check if health checks are already registered
        var healthCheckServiceDescriptor = services.FirstOrDefault(s => s.ServiceType == typeof(Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckService));

        if (healthCheckServiceDescriptor == null)
        {
            // Add health checks only if not already registered
            var healthChecksBuilder = services.AddHealthChecks()
                .AddCheck("self", () => Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult.Healthy("Application is running"));

            // Add Redis health check if Redis is configured
            var redisConnectionString = configuration.GetConnectionString("Redis")
                                        ?? configuration.GetSection("Redis")["ConnectionString"]
                                        ?? Environment.GetEnvironmentVariable("REDIS_CONNECTION_STRING")
                                        ?? Environment.GetEnvironmentVariable("Redis__ConnectionString")
                                        ?? "*************:6379";
            if (!string.IsNullOrEmpty(redisConnectionString))
            {
                healthChecksBuilder.AddRedis(redisConnectionString, name: "redis", tags: new[] { "cache", "infrastructure" });
            }
        }

        return services;
    }

    /// <summary>
    /// Register minimal services for testing/examples
    /// </summary>
    public static IServiceCollection AddMinimalServices(this IServiceCollection services, IConfiguration configuration)
    {
        return services
            .AddPollyHttpClients(configuration)
            .AddCoreInfrastructure()
            .AddDataServices(configuration)
            .AddMarketDataServices();
    }

    /// <summary>
    /// Register services for cache operations only
    /// </summary>
    public static IServiceCollection AddCacheServices(this IServiceCollection services, IConfiguration? cfg = null)
    {
        services.AddHttpClient();
        services.AddSingleton<IRateLimitPolicyFactory, RateLimitPolicyFactory>();

        // Database contexts
        services.AddDbContext<IndexCacheDbContext>(options =>
        {
            var connectionString = "Data Source=index_cache.db";
            options.UseSqlite(connectionString);
        });
        services.AddDbContext<StockBarCacheDbContext>(options =>
        {
            var connectionString = "Data Source=stock_cache.db";
            options.UseSqlite(connectionString);
        });

        // Cache services
        // FIXED: Changed to Singleton to resolve DI lifetime mismatch with MarketDataService
        services.AddSingleton<IIndexCacheService, IndexCacheService>();
        services.AddSingleton<IStockBarCacheService, StockBarCacheService>();
        services.AddScoped<ICacheMaintenanceService, CacheMaintenanceService>();

        // Bulk insert optimization service for ~5x performance improvement
        services.AddSingleton<IBulkInsertOptimizationService, BulkInsertOptimizationService>();

        // Database initialization service
        services.AddScoped<IDatabaseInitializationService, DatabaseInitializationService>();

        // Database configuration service
        services.AddScoped<IDatabaseConfigurationService, DatabaseConfigurationService>();

        return services;
    }

    /// <summary>
    /// Register services for signal generation testing
    /// </summary>
    public static IServiceCollection AddSignalTestingServices(this IServiceCollection services, IConfiguration configuration)
    {
        return services
            .AddMinimalServices(configuration)
            .AddTradingServices();
    }

    /// <summary>
    /// Register the refactored trading services that replace EnhancedTradingService
    /// </summary>
    public static IServiceCollection AddRefactoredTradingServices(this IServiceCollection services)
    {
        // NOTE: Dependencies (IVolatilityManager, ITickVolatilityGuard, IDiscordNotificationService)
        // are already registered by AddFullTradingSystem -> AddEnhancedTradingServices()

        // Register the focused trading services with all their dependencies
        services.AddScoped<IEquityTradingCycleService, EquityTradingCycleService>();
        services.AddScoped<IOptionsOverlayService, OptionsOverlayService>();
        services.AddScoped<IPortfolioManagementService, PortfolioManagementService>();
        services.AddScoped<IRealTimeMonitoringService, RealTimeMonitoringService>();

        // Register the orchestrator as the main ITradingService implementation
        services.AddScoped<ITradingCycleOrchestrator, TradingCycleOrchestrator>();
        services.AddScoped<ITradingService>(provider => provider.GetRequiredService<ITradingCycleOrchestrator>());

        return services;
    }

    /// <summary>
    /// Register the trading service implementation (uses proper live/paper trading based on configuration)
    /// </summary>
    public static IServiceCollection AddTradingServiceImplementation(this IServiceCollection services, bool useEnhanced = true, bool useRefactored = false)
    {
        // Use refactored architecture if requested
        if (useRefactored)
        {
            return services.AddRefactoredTradingServices();
        }

        // Use proper trading service that respects live/paper configuration
        if (useEnhanced)
        {
            services.AddScoped<ITradingService, EnhancedTradingService>();
        }
        else
        {
            services.AddScoped<ITradingService, SimpleTradingService>();
        }
        return services;
    }

    /// <summary>
    /// Initializes all databases for the service provider
    /// </summary>
    public static async Task InitializeDatabasesAsync(this IServiceProvider serviceProvider)
    {
        using var scope = serviceProvider.CreateScope();
        var dbInitService = scope.ServiceProvider.GetService<IDatabaseInitializationService>();

        if (dbInitService != null)
        {
            await dbInitService.InitializeAllDatabasesAsync();
        }
    }

    /// <summary>
    /// Register security services including secret providers
    /// </summary>
    public static IServiceCollection AddSecurityServices(this IServiceCollection services, IConfiguration configuration)
    {
        // Secret provider will be registered as singleton in Program.cs
        // This method can be used for additional security-related services

        return services;
    }

    /// <summary>
    /// Helper method to get Polygon API key from configuration or environment variables
    /// </summary>
    private static string GetPolygonApiKey(IConfiguration configuration)
    {
        return configuration["Polygon:ApiKey"]
               ?? configuration["POLY_API_KEY"]
               ?? Environment.GetEnvironmentVariable("POLY_API_KEY")
               ?? Environment.GetEnvironmentVariable("POLYGON_API_KEY")
               ?? throw new InvalidOperationException("Polygon API key not configured. Set Polygon:ApiKey in appsettings.json or POLY_API_KEY/POLYGON_API_KEY environment variable");
    }

    /// <summary>
    /// Configure secret provider based on environment variables
    /// </summary>
    public static ISecretProvider CreateSecretProvider(Microsoft.Extensions.Logging.ILogger? logger = null)
    {
        var vaultAddr = Environment.GetEnvironmentVariable("VAULT_ADDR");
        var vaultToken = Environment.GetEnvironmentVariable("VAULT_TOKEN");

        if (!string.IsNullOrEmpty(vaultAddr) && !string.IsNullOrEmpty(vaultToken))
        {
            try
            {
                logger?.LogInformation("Initializing HashiCorp Vault secret provider at {VaultAddress}", vaultAddr);
                return new VaultSecretProvider(vaultAddr, vaultToken, "secret/data/sma", logger as ILogger<VaultSecretProvider>);
            }
            catch (Exception ex)
            {
                logger?.LogError(ex, "Failed to initialize Vault secret provider, falling back to environment variables");
            }
        }

        logger?.LogInformation("Using environment variable secret provider");
        return new EnvSecretProvider(logger as ILogger<EnvSecretProvider>);
    }

    /// <summary>
    /// Register enhanced services migration management
    /// </summary>
    public static IServiceCollection AddEnhancedServicesMigration(this IServiceCollection services)
    {
        services.AddSingleton<IEnhancedServicesMigrationService, EnhancedServicesMigrationService>();
        return services;
    }
}
